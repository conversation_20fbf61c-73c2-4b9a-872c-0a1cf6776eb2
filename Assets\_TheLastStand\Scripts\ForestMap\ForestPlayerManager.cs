using UnityEngine;
using System.Collections.Generic;
using Mirror;

public class ForestPlayerManager : MonoBehaviour
{
    [Header("Prefabs")]
    [SerializeField] private GameObject clientPlayerPrefab;
    [SerializeField] private GameObject replicatedPlayerPrefabOne;
    [SerializeField] private GameObject replicatedPlayerPrefabTwo;
    [SerializeField] private GameObject replicatedPlayerPrefabThree;
    [Header("Scene References")]
    [SerializeField] private Camera playerCameraToAssign;

    [Header("Spawn Points")]
    [SerializeField] private Transform localPlayerSpawnPoint;
    [SerializeField] private List<Transform> replicatedPlayerSpawnPoints = new List<Transform>();

    private ForestIntroPlayer _localPlayerInstance;
    private List<ForestIntroReplicatedPlayer> _replicatedPlayerInstances = new List<ForestIntroReplicatedPlayer>();
    private List<GameObject> _networkedPlayers = new List<GameObject>();
    private bool _hasSpawnedNetworkedPlayers = false;

    void Start()
    {
        // Only spawn non-networked intro players if we're not in multiplayer mode
        if (!MyNetworkManager.isMultiplayer)
        {
            SpawnLocalPlayer();
            SpawnReplicatedPlayers();
        }
        else
        {
            // In multiplayer, wait for network spawning to complete
            Debug.Log("ForestPlayerManager: Waiting for networked players to spawn...");
        }
    }

    void SpawnLocalPlayer()
    {
        if (clientPlayerPrefab == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'clientPlayerPrefab' is not assigned.", this);
            return;
        }
        if (localPlayerSpawnPoint == null)
        {
            Debug.LogError("ForestIntroPlayerManager: 'localPlayerSpawnPoint' is not assigned. Cannot spawn local player.", this);
            return;
        }

        GameObject localPlayerGO = Instantiate(clientPlayerPrefab, localPlayerSpawnPoint.position, localPlayerSpawnPoint.rotation, localPlayerSpawnPoint);
        _localPlayerInstance = localPlayerGO.GetComponent<ForestIntroPlayer>();

        if (_localPlayerInstance == null)
        {
            Debug.LogError("ForestIntroPlayerManager: The 'clientPlayerPrefab' does not have a ForestIntroPlayer component.", localPlayerGO);
            Destroy(localPlayerGO); // Cleanup
        }
        else
        {
            localPlayerGO.name = "Local_ForestIntroPlayer";
            if (playerCameraToAssign != null)
            {
                _localPlayerInstance.InitializePlayerCamera(playerCameraToAssign);
            }
            else
            {
                Debug.LogWarning("ForestPlayerManager: 'playerCameraToAssign' is not assigned in the Inspector. Local player may use Camera.main or have no camera.", this);
                _localPlayerInstance.InitializePlayerCamera(null); // Call with null to allow fallback in player script
            }
        }
    }

    void SpawnReplicatedPlayers()
    {
        List<GameObject> availableReplicatedPrefabs = new List<GameObject>();
        if (replicatedPlayerPrefabOne != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabOne);
        if (replicatedPlayerPrefabTwo != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabTwo);
        if (replicatedPlayerPrefabThree != null) availableReplicatedPrefabs.Add(replicatedPlayerPrefabThree);

        if (availableReplicatedPrefabs.Count == 0)
        {
            Debug.LogError("ForestPlayerManager: No replicated player prefabs assigned (One, Two, or Three). Cannot spawn replicated players.", this);
            return;
        }

        if (replicatedPlayerSpawnPoints.Count == 0)
        {
            Debug.LogWarning("ForestPlayerManager: No spawn points provided for replicated players.", this);
            return;
        }

        int numberOfPlayersToSpawn = Mathf.Min(replicatedPlayerSpawnPoints.Count, availableReplicatedPrefabs.Count);

        if (replicatedPlayerSpawnPoints.Count > availableReplicatedPrefabs.Count)
        {
            Debug.LogWarning($"ForestPlayerManager: More spawn points ({replicatedPlayerSpawnPoints.Count}) than available unique replicated prefabs ({availableReplicatedPrefabs.Count}). Only spawning {availableReplicatedPrefabs.Count} replicated players.", this);
        }

        for (int i = 0; i < numberOfPlayersToSpawn; i++)
        {
            Transform spawnPoint = replicatedPlayerSpawnPoints[i];
            GameObject prefabToSpawn = availableReplicatedPrefabs[i];

            if (spawnPoint == null)
            {
                Debug.LogWarning($"ForestPlayerManager: Replicated player spawn point at index {i} is null. Skipping.", this);
                continue;
            }

            if (prefabToSpawn == null)
            {
                Debug.LogWarning($"ForestPlayerManager: Replicated player prefab for index {i} is unexpectedly null. Skipping.", this);
                continue;
            }

            // Player 1 (Host) - no special rotation here.
            // Player 2 (Replicated Index 0) - spawns with default spawn point rotation.
            // Player 3 (Replicated Index 1) - needs 180 Y rotation.
            // Player 4 (Replicated Index 2) - needs 180 Y rotation.
            // Instantiate with spawn point's initial rotation first.
            GameObject replicatedPlayerGO = Instantiate(prefabToSpawn, spawnPoint.position, spawnPoint.rotation, spawnPoint);
            ForestIntroReplicatedPlayer replicatedPlayer = replicatedPlayerGO.GetComponent<ForestIntroReplicatedPlayer>();

            if (replicatedPlayer == null)
            {
                Debug.LogError($"ForestPlayerManager: The prefab \"{prefabToSpawn.name}\" does not have a ForestIntroReplicatedPlayer component. Spawned for index {i}.", replicatedPlayerGO);
                Destroy(replicatedPlayerGO); // Cleanup
            }
            else
            {
                replicatedPlayerGO.name = $"Replicated_ForestIntroPlayer_{i}_{prefabToSpawn.name}";
                _replicatedPlayerInstances.Add(replicatedPlayer);

                // Set initial Y rotation offset for Player 3 (index 1) and Player 4 (index 2)
                if (i == 1 || i == 2)
                {
                    replicatedPlayer.SetInitialYRotationOffset(180f);
                }
            }
        }
        
        if (clientPlayerPrefab != null && clientPlayerPrefab.GetComponent<ForestIntroPlayer>() != null)
        {
            clientPlayerPrefab.GetComponent<ForestIntroPlayer>().LockCursor();
        }
    }

    /// <summary>
    /// Called by MyNetworkManager to spawn a networked player in the forest scene
    /// </summary>
    public void SpawnNetworkedPlayer(NetworkConnectionToClient conn, GameObject networkPlayerPrefab)
    {
        if (networkPlayerPrefab == null)
        {
            Debug.LogError("ForestPlayerManager: Network player prefab is null.");
            return;
        }

        // Determine spawn point based on connection
        Transform spawnPoint = GetSpawnPointForConnection(conn);

        if (spawnPoint == null)
        {
            Debug.LogError("ForestPlayerManager: No available spawn point for networked player.");
            return;
        }

        // Debug spawn point hierarchy
        Debug.Log($"ForestPlayerManager: Using spawn point '{spawnPoint.name}' for connection {conn.connectionId}");
        Debug.Log($"ForestPlayerManager: Spawn point hierarchy: {GetTransformHierarchy(spawnPoint)}");

        // Spawn the networked player with identity transform first, then parent and configure
        GameObject networkedPlayer = Instantiate(networkPlayerPrefab, Vector3.zero, Quaternion.identity);

        // Parent to spawn point and reset local transform to ensure correct local coordinates
        Debug.Log($"ForestPlayerManager: Before parenting - Player world pos: {networkedPlayer.transform.position}, rot: {networkedPlayer.transform.rotation.eulerAngles}");
        Debug.Log($"ForestPlayerManager: Spawn point '{spawnPoint.name}' world pos: {spawnPoint.position}, rot: {spawnPoint.rotation.eulerAngles}");

        networkedPlayer.transform.SetParent(spawnPoint);
        networkedPlayer.transform.localPosition = Vector3.zero;
        networkedPlayer.transform.localRotation = Quaternion.identity;

        Debug.Log($"ForestPlayerManager: After parenting and reset - Player local pos: {networkedPlayer.transform.localPosition}, rot: {networkedPlayer.transform.localRotation.eulerAngles}");

        // Configure the networked player for the forest intro
        ConfigureNetworkedPlayerForIntro(networkedPlayer, conn);

        // Register with Mirror networking
        NetworkServer.AddPlayerForConnection(conn, networkedPlayer);

        // Track the spawned player
        _networkedPlayers.Add(networkedPlayer);

        // Initialize the player through the network manager
        MyNetworkManager.instance.InitializeSpawnedPlayer(conn, networkedPlayer);

        Debug.Log($"ForestPlayerManager: Successfully spawned networked player '{networkedPlayer.name}' at {spawnPoint.name} with local position: {networkedPlayer.transform.localPosition} and local rotation: {networkedPlayer.transform.localRotation}");

        // Mark that we've started spawning networked players
        _hasSpawnedNetworkedPlayers = true;
    }

    private Transform GetSpawnPointForConnection(NetworkConnectionToClient conn)
    {
        // For the local server connection (host), use the local player spawn point
        if (conn == NetworkServer.localConnection)
        {
            return localPlayerSpawnPoint;
        }

        // For remote connections, use replicated player spawn points
        int connectionIndex = GetConnectionIndex(conn);

        if (connectionIndex >= 0 && connectionIndex < replicatedPlayerSpawnPoints.Count)
        {
            return replicatedPlayerSpawnPoints[connectionIndex];
        }

        // Fallback to local spawn point if no specific point available
        Debug.LogWarning($"ForestPlayerManager: No specific spawn point for connection {conn.connectionId}, using local spawn point.");
        return localPlayerSpawnPoint;
    }

    private int GetConnectionIndex(NetworkConnectionToClient conn)
    {
        // Simple index assignment based on connection ID
        // In a more sophisticated system, you might track this differently
        return conn.connectionId - 1; // Subtract 1 because host is connection 0
    }

    private void ConfigureNetworkedPlayerForIntro(GameObject networkedPlayer, NetworkConnectionToClient conn)
    {
        // Ensure the networked player has the necessary components for the intro
        MyClient myClient = networkedPlayer.GetComponent<MyClient>();
        if (myClient == null)
        {
            Debug.LogError("ForestPlayerManager: Networked player missing MyClient component.");
            return;
        }

        // Add ForestIntroPlayer component if not present
        ForestIntroPlayer introPlayer = networkedPlayer.GetComponent<ForestIntroPlayer>();
        if (introPlayer == null)
        {
            introPlayer = networkedPlayer.AddComponent<ForestIntroPlayer>();
        }

        // Add HelicopterTransformConstraint component for helicopter flight constraints
        HelicopterTransformConstraint constraint = networkedPlayer.GetComponent<HelicopterTransformConstraint>();
        if (constraint == null)
        {
            constraint = networkedPlayer.AddComponent<HelicopterTransformConstraint>();
            Debug.Log($"ForestPlayerManager: Added HelicopterTransformConstraint to {networkedPlayer.name}");
        }

        // For testing: Immediately activate constraints to ensure proper positioning
        // This will be managed by HelicopterConstraintManager in normal operation
        if (NetworkServer.active && constraint != null)
        {
            // Wait a frame then activate constraints to ensure proper initialization
            StartCoroutine(ActivateConstraintsAfterDelay(constraint));
        }

        // Configure camera for local player
        if (conn == NetworkServer.localConnection && playerCameraToAssign != null)
        {
            introPlayer.InitializePlayerCamera(playerCameraToAssign);
            _localPlayerInstance = introPlayer;
        }

        // Set the player's name for identification
        networkedPlayer.name = conn == NetworkServer.localConnection ? "NetworkedHost_ForestPlayer" : $"NetworkedClient_ForestPlayer_{conn.connectionId}";

        // Note: Local transform is already set to (0,0,0) in SpawnNetworkedPlayer method
    }

    private System.Collections.IEnumerator ActivateConstraintsAfterDelay(HelicopterTransformConstraint constraint)
    {
        // Wait a few frames to ensure all components are properly initialized
        yield return null;
        yield return null;

        if (constraint != null)
        {
            Debug.Log($"ForestPlayerManager: Activating constraints for {constraint.gameObject.name} after initialization delay");
            constraint.ActivateConstraints();
        }
    }

    private string GetTransformHierarchy(Transform t)
    {
        if (t == null) return "null";

        string hierarchy = t.name;
        Transform parent = t.parent;

        while (parent != null)
        {
            hierarchy = parent.name + "/" + hierarchy;
            parent = parent.parent;
        }

        return hierarchy;
    }

    void Update()
    {
        // Handle both networked and non-networked scenarios
        if (MyNetworkManager.isMultiplayer)
        {
            HandleNetworkedPlayerUpdates();
        }
        else
        {
            HandleLocalPlayerUpdates();
        }
    }

    private void HandleNetworkedPlayerUpdates()
    {
        // In multiplayer, the actual player synchronization is handled by Mirror
        // We only need to manage local intro-specific behavior

        if (_localPlayerInstance != null && _networkedPlayers.Count > 1)
        {
            // Get local player orientation for any local intro effects
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldZRotation = localPlayerOrientation.eulerAngles.z;

            // Note: In a real networked scenario, this data would be synchronized
            // through Mirror's networking system rather than direct method calls
        }
    }

    private void HandleLocalPlayerUpdates()
    {
        // This section simulates network updates for single-player mode.
        // In single-player, we simulate multiplayer behavior with replicated players.

        if (_localPlayerInstance != null && _replicatedPlayerInstances.Count > 0)
        {
            // 1. Get the Z rotation from the local client player.
            // Using GetWorldOrientation() and then taking .eulerAngles.z to be consistent
            // with the replicated player potentially needing only the Z component of a full quaternion.
            Quaternion localPlayerOrientation = _localPlayerInstance.GetWorldOrientation();
            float localPlayerWorldZRotation = localPlayerOrientation.eulerAngles.z;

            // 2. "Send" this Z rotation to all replicated players.
            foreach (ForestIntroReplicatedPlayer replicatedPlayer in _replicatedPlayerInstances)
            {
                if (replicatedPlayer != null)
                {
                    // Here we directly call the method that uses the Z rotation.
                    // The Replicated Player script itself ensures only its Z world axis is changed.
                    replicatedPlayer.SetVisualWorldZRotation(localPlayerWorldZRotation);
                }
            }
        }
    }

    /// <summary>
    /// Clean up spawned players when the scene is unloaded
    /// </summary>
    private void OnDestroy()
    {
        // Clean up non-networked players
        if (_localPlayerInstance != null && !MyNetworkManager.isMultiplayer)
        {
            Destroy(_localPlayerInstance.gameObject);
        }

        foreach (var replicatedPlayer in _replicatedPlayerInstances)
        {
            if (replicatedPlayer != null && !MyNetworkManager.isMultiplayer)
            {
                Destroy(replicatedPlayer.gameObject);
            }
        }

        // Clear lists
        _replicatedPlayerInstances.Clear();
        _networkedPlayers.Clear();
    }

    /// <summary>
    /// Get the current local player instance (networked or non-networked)
    /// </summary>
    public ForestIntroPlayer GetLocalPlayerInstance()
    {
        return _localPlayerInstance;
    }

    /// <summary>
    /// Check if the manager has spawned any players
    /// </summary>
    public bool HasSpawnedPlayers()
    {
        return _localPlayerInstance != null || _networkedPlayers.Count > 0;
    }

    /// <summary>
    /// Get count of active players in the scene
    /// </summary>
    public int GetActivePlayerCount()
    {
        if (MyNetworkManager.isMultiplayer)
        {
            return _networkedPlayers.Count;
        }
        else
        {
            int count = 0;
            if (_localPlayerInstance != null) count++;
            count += _replicatedPlayerInstances.Count;
            return count;
        }
    }
}
